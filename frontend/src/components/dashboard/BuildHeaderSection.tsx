import React from 'react';
import { useNavigate } from 'react-router-dom';

interface BuildHeaderSectionProps {
  onBackToInterview?: () => void;
  onPublish?: () => void;
  uuid?: string;
}

const BuildHeaderSection: React.FC<BuildHeaderSectionProps> = () => {
  const navigate = useNavigate();

  // Settings icon matching the target design
  const SettingsIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M19.4 15C19.2 15.3 19.1 15.7 19.1 16.1C19.1 16.5 19.2 16.9 19.4 17.2L18.2 18.4C18 18.6 18 18.9 18.2 19.1L19.9 21.8C20.1 22 20.4 22.1 20.6 22L22.1 21.3C22.5 21.6 22.9 21.9 23.4 22.1L23.6 23.8C23.7 24.1 23.9 24.3 24.2 24.3H27.8C28.1 24.3 28.3 24.1 28.4 23.8L28.6 22.1C29.1 21.9 29.5 21.6 29.9 21.3L31.4 22C31.7 22.1 32 22 32.2 21.8L33.9 19.1C34.1 18.9 34.1 18.6 33.9 18.4L32.7 17.2C32.9 16.9 33 16.5 33 16.1C33 15.7 32.9 15.3 32.7 15L33.9 13.8C34.1 13.6 34.1 13.3 33.9 13.1L32.2 10.4C32 10.2 31.7 10.1 31.4 10.2L29.9 10.9C29.5 10.6 29.1 10.3 28.6 10.1L28.4 8.4C28.3 8.1 28.1 7.9 27.8 7.9H24.2C23.9 7.9 23.7 8.1 23.6 8.4L23.4 10.1C22.9 10.3 22.5 10.6 22.1 10.9L20.6 10.2C20.3 10.1 20 10.2 19.8 10.4L18.1 13.1C17.9 13.3 17.9 13.6 18.1 13.8L19.3 15H19.4Z" stroke="currentColor" strokeWidth="1"/>
    </svg>
  );

  return (
    <div className="w-full bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          {/* Left section - empty for balance */}
          <div className="w-8"></div>

          {/* Center - Logo */}
          <div
            className="cursor-pointer"
            onClick={() => navigate("/")}
          >
            <h1 className="text-2xl font-bold text-black">Mergen AI</h1>
          </div>

          {/* Right section - Settings */}
          <div className="cursor-pointer text-gray-600 hover:text-gray-800 transition-colors">
            <SettingsIcon />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuildHeaderSection;

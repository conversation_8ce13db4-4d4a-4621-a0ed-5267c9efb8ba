import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Box, IconButton } from "@mui/material";
import { ArrowBack as ArrowBackIcon, Menu as MenuIcon } from "@mui/icons-material";

interface BuildHeaderSectionProps {
  onBackToInterview: () => void;
  onPublish: () => void;
  uuid?: string;
}

const BuildHeaderSection: React.FC<BuildHeaderSectionProps> = ({
  onBackToInterview,
  onPublish,
  uuid
}) => {
  const navigate = useNavigate();
  const [openMobileMenu, setOpenMobileMenu] = useState(false);

  // Simple logo placeholder
  const LogoPlaceholder = () => (
    <div className="w-[200px] h-[50px] bg-gray-800 text-white flex items-center justify-center rounded cursor-pointer">
      <span className="font-bold text-lg">MERGEN AI</span>
    </div>
  );

  // Simple settings icon placeholder
  const SettingsIcon = () => (
    <svg width="27" height="27" viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M13.5 8.5C10.7 8.5 8.5 10.7 8.5 13.5C8.5 16.3 10.7 18.5 13.5 18.5C16.3 18.5 18.5 16.3 18.5 13.5C18.5 10.7 16.3 8.5 13.5 8.5Z" stroke="currentColor" strokeWidth="2"/>
      <path d="M13.5 1L16.09 7.26L22 4.27L19.91 10.09L26.5 13.5L19.91 16.91L22 22.73L16.09 19.74L13.5 26L10.91 19.74L5 22.73L7.09 16.91L0.5 13.5L7.09 10.09L5 4.27L10.91 7.26L13.5 1Z" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    navigate('/');
  };

  return (
    <>
      <div className="flex justify-between shadow-[0px_3px_8.4px_0px_rgba(0,0,0,0.25)] px-6 sm:px-14 py-8 sm:py-11 bg-white">
        {/* Left section - Back button */}
        <div className="flex-1 flex justify-start items-center">
          <Button
            onClick={onBackToInterview}
            startIcon={<ArrowBackIcon />}
            sx={{ 
              color: '#667eea',
              textTransform: 'none',
              fontWeight: 'bold'
            }}
          >
            Edit Interview
          </Button>
        </div>
        
        {/* Logo - centered */}
        <div className="flex-1 flex justify-center">
          <div onClick={() => navigate("/")}>
            <LogoPlaceholder />
          </div>
        </div>
        
        {/* Right section - desktop */}
        <div className="lg:flex justify-end gap-2.5 items-center flex-1 hidden">
          <Button
            variant="contained"
            onClick={onPublish}
            sx={{
              bgcolor: '#4361ee',
              '&:hover': { bgcolor: '#0d47a1' },
              color: 'white',
              fontWeight: 'bold',
              textTransform: 'none',
              mr: 2
            }}
          >
            Publish
          </Button>
          <div className="cursor-pointer" onClick={handleLogout}>
            <SettingsIcon />
          </div>
        </div>
        
        {/* Mobile menu button */}
        <div className="lg:hidden flex items-center">
          <IconButton
            onClick={() => setOpenMobileMenu(!openMobileMenu)}
            sx={{ color: '#1E1E1E' }}
          >
            <MenuIcon />
          </IconButton>
        </div>
      </div>

      {/* Mobile Menu */}
      {openMobileMenu && (
        <div className="lg:hidden absolute top-full left-0 right-0 bg-white shadow-lg z-50 border-t">
          <div className="p-4">
            <div className="space-y-4">
              <button
                onClick={() => {
                  onBackToInterview();
                  setOpenMobileMenu(false);
                }}
                className="block w-full text-left py-3 px-4 hover:bg-gray-100 rounded"
              >
                Edit Interview
              </button>
              
              <button
                onClick={() => {
                  onPublish();
                  setOpenMobileMenu(false);
                }}
                className="block w-full text-left py-3 px-4 hover:bg-gray-100 rounded text-blue-600"
              >
                Publish
              </button>
              
              <button
                onClick={() => {
                  handleLogout();
                  setOpenMobileMenu(false);
                }}
                className="block w-full text-left py-3 px-4 hover:bg-gray-100 rounded text-red-600"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BuildHeaderSection;

import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { IconButton, CircularProgress, Typography, Box } from "@mui/material";
import { 
  ExpandLess as ExpandLessIcon, 
  ExpandMore as ExpandMoreIcon,
  History as HistoryIcon 
} from "@mui/icons-material";
import SearchBar from "./SearchBar";

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface BuildChatboxProps {
  messages: ChatMessage[];
  inputValue: string;
  setInputValue: (value: string) => void;
  onSendMessage: () => void;
  isSubmitting: boolean;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onOpenHistory: () => void;
}

const BuildChatbox: React.FC<BuildChatboxProps> = ({
  messages,
  inputValue,
  setInputValue,
  onSendMessage,
  isSubmitting,
  isExpanded,
  onToggleExpanded,
  onOpenHistory,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Simple icon placeholders
  const AIIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="8" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M10 6V14M6 10H14" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const UserIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      <circle cx="10" cy="7" r="4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 w-[90%] sm:w-[70%] md:w-[50%] z-40">
      <motion.div
        initial={false}
        animate={{
          height: isExpanded ? '400px' : '80px',
        }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        className="bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden"
        style={{
          background: 'linear-gradient(168.12deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 98.85%)',
          backdropFilter: 'blur(40px)',
          boxShadow: '0px 4px 24px -1px rgba(136, 136, 136, 0.12), 0 12px 40px rgba(0, 0, 0, 0.15)',
        }}
      >
        {/* Header with Input */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            {/* History Button */}
            <IconButton
              onClick={onOpenHistory}
              size="small"
              sx={{
                color: '#667eea',
                '&:hover': { bgcolor: 'rgba(102, 126, 234, 0.1)' }
              }}
            >
              <HistoryIcon />
            </IconButton>

            {/* Search Input */}
            <div className="flex-1">
              <SearchBar
                color="#667eea"
                border={false}
                inputValue={inputValue}
                handleSearch={onSendMessage}
                setInputValue={setInputValue}
                placeholder="Ask about your project..."
                resize={false}
                disable={isSubmitting}
              />
            </div>

            {/* Expand/Collapse Button */}
            <IconButton
              onClick={onToggleExpanded}
              size="small"
              sx={{
                color: '#667eea',
                '&:hover': { bgcolor: 'rgba(102, 126, 234, 0.1)' }
              }}
            >
              {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </div>
        </div>

        {/* Chat Messages - Only show when expanded */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="flex-1 overflow-hidden"
            >
              <div 
                className="h-80 overflow-y-auto p-4 space-y-4"
                style={{ 
                  scrollbarWidth: 'thin', 
                  scrollbarColor: 'rgba(102, 126, 234, 0.3) transparent' 
                }}
              >
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <AIIcon />
                    <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
                      Start a conversation about your project!
                    </Typography>
                  </div>
                ) : (
                  messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-2xl ${
                          message.type === 'user'
                            ? 'bg-[#667eea] text-white'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <div className="flex items-start gap-2">
                          <div className={`mt-1 ${message.type === 'user' ? 'text-white' : 'text-gray-600'}`}>
                            {message.type === 'user' ? <UserIcon /> : <AIIcon />}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm leading-relaxed whitespace-pre-wrap">
                              {message.content}
                            </p>
                            <span className={`text-xs mt-1 block ${
                              message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                            }`}>
                              {formatTime(message.timestamp)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
                
                {/* Loading indicator */}
                {isSubmitting && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-start"
                  >
                    <div className="bg-gray-100 p-3 rounded-2xl">
                      <div className="flex items-center gap-2">
                        <CircularProgress size={16} sx={{ color: '#667eea' }} />
                        <Typography variant="body2" color="text.secondary">
                          AI is thinking...
                        </Typography>
                      </div>
                    </div>
                  </motion.div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default BuildChatbox;

import { useState, useRef, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import SearchBar from "./SearchBar";

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface BuildFooterSectionProps {
  messages: ChatMessage[];
  inputValue: string;
  setInputValue: (value: string) => void;
  onSendMessage: () => void;
  isSubmitting: boolean;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onOpenHistory: () => void;
}

const BuildFooterSection: React.FC<BuildFooterSectionProps> = ({
  messages,
  inputValue,
  setInputValue,
  onSendMessage,
  isSubmitting,
  isExpanded,
  onToggleExpanded,
  onOpenHistory,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Icon components matching the target design
  const FlowerIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="8" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M10 6V14M6 10H14" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const MagnifyIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="9" cy="9" r="7" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );

  const ArrowIcon = () => (
    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 1L19 8.5L12 16M19 8.5H1" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const ExpandIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6 9L10 13L14 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const CollapseIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14 11L10 7L6 11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const HistoryIcon = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="8" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M10 6V10L13 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 w-[90%] sm:w-[70%] md:w-[50%] z-40">
      <motion.div
        initial={false}
        animate={{
          height: isExpanded ? 'auto' : '80px',
        }}
        transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        className="glass-effect rounded-[25px] border border-gray-200 overflow-hidden"
      >
        <div className="p-6 sm:p-8">
          {/* Chat Messages Display - Only show when expanded */}
          <AnimatePresence>
            {isExpanded && messages.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mb-6 space-y-4 max-h-60 overflow-y-auto"
                style={{ 
                  scrollbarWidth: 'thin', 
                  scrollbarColor: 'rgba(102, 126, 234, 0.3) transparent' 
                }}
              >
                {messages.map((message, index) => (
                  <div
                    key={message.id}
                    className={`flex ${
                      message.type === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.type === 'user'
                          ? 'bg-[#353535] text-white'
                          : 'bg-white text-black border'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <span className="text-xs opacity-70">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Search Input */}
          <div className="mb-4">
            <SearchBar
              color="#1E1E1E"
              border={false}
              inputValue={inputValue}
              handleSearch={onSendMessage}
              setInputValue={setInputValue}
              placeholder="write your message here..."
              resize={true}
              disable={isSubmitting}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
            {/* Left side buttons */}
            <div className="flex gap-3">
              <button
                className="bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
                onClick={onOpenHistory}
              >
                <HistoryIcon />
                <span className="text-sm font-medium">History</span>
              </button>
              
              <button
                className="bg-white shadow-[0px_2px_6.7px_0px_rgba(0,0,0,0.25)] rounded-[10px] px-4 py-2 flex items-center gap-2 hover:bg-gray-50 transition-colors"
                onClick={onToggleExpanded}
              >
                {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
                <span className="text-sm font-medium">{isExpanded ? 'Collapse' : 'Expand'}</span>
              </button>
            </div>

            {/* Right side - Status or additional actions */}
            <div className="flex items-center gap-2">
              {isSubmitting && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                  <span>Sending...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default BuildFooterSection;

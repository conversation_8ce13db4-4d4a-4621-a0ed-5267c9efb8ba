import React from 'react';
import SphereEngineWorkspace from '../SphereEngineWorkspace';

interface BuildWorkspaceAreaProps {
  hasCodeGenerated: boolean;
  currentWorkspaceId: string | null;
  projectId: string;
  interviewUuid?: string;
  onWorkspaceReady: (workspaceId: string) => void;
  onError: (error: string) => void;
}

const BuildWorkspaceArea: React.FC<BuildWorkspaceAreaProps> = ({
  hasCodeGenerated,
  currentWorkspaceId,
  projectId,
  interviewUuid,
  onWorkspaceReady,
  onError,
}) => {
  return (
    <div className="w-full h-full bg-white">
      {!hasCodeGenerated ? (
        <div className="h-full flex flex-col items-center justify-center text-gray-500 bg-gray-50">
          <div className="text-center">
            <div className="mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-400 mx-auto"></div>
            </div>
            <h3 className="text-lg font-medium mb-2">No References Found</h3>
            <p className="text-sm text-gray-400">
              Your workspace will appear here once code generation is complete
            </p>
          </div>
        </div>
      ) : (
        <div className="h-full">
          <SphereEngineWorkspace
            projectId={projectId}
            interviewUuid={interviewUuid}
            workspaceId={currentWorkspaceId || undefined}
            onWorkspaceReady={onWorkspaceReady}
            onError={onError}
            height="100%"
            minHeight="600px"
          />
        </div>
      )}
    </div>
  );
};

export default BuildWorkspaceArea;

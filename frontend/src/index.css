@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  /* Primary Colors */
  --color-primary-dark: #1E1E1E;
  --color-secondary-dark: #353535;
  --color-tertiary-dark: #414141;
  --color-text-primary: #222222;

  /* Background Colors */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F6F6F6;
  --color-bg-tertiary: #ECECEC;

  /* Border Colors */
  --color-border-primary: #939393;
  --color-border-secondary: #9E9E9E;

  /* Dynamic Card Colors */
  --color-card-yellow: #F8DE7E;
  --color-card-peach: #E8AE77;
  --color-card-gray: #D3D3D3;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

* {
  box-sizing: border-box;
}

/* Custom Scrollbar Styles */
.scrollbar-custom {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Hide Scrollbar */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Glass Morphism Effects */
.glass-effect {
  background: linear-gradient(168.12deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 98.85%);
  backdrop-filter: blur(40px);
  box-shadow: 0px 4px 24px -1px rgba(136, 136, 136, 0.12);
}

/* Text Truncation Utilities */
.truncate-text {
  display: -webkit-box;
  -webkit-line-clamp: 8;
  line-clamp: 8;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.truncate-item {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

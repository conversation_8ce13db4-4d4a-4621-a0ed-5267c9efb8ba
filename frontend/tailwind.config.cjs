/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          dark: '#1E1E1E',
          secondary: '#353535',
          tertiary: '#414141',
        },
        text: {
          primary: '#222222',
        },
        bg: {
          primary: '#FFFFFF',
          secondary: '#F6F6F6',
          tertiary: '#ECECEC',
        },
        border: {
          primary: '#939393',
          secondary: '#9E9E9E',
        },
        card: {
          yellow: '#F8DE7E',
          peach: '#E8AE77',
          gray: '#D3D3D3',
        }
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'charter': ['Charter', 'serif'],
        'tw-cen-mt': ['Tw-Cen-MT', 'sans-serif'],
      },
      backdropBlur: {
        '40': '40px',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in-left': 'slideInLeft 0.6s ease-in-out',
        'slide-in-right': 'slideInRight 0.6s ease-in-out',
        'spin': 'spin 1s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
